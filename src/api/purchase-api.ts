import { httpsCallable } from 'firebase/functions';

import { firebaseFunctions } from '@/root-context';

import { getCurrentUserId } from './auth-api';

export interface MakePurchaseAsBuyerResponse {
  success: boolean;
  message: string;
  lockedAmount: number;
  orderAmount: number;
  lockPercentage: number;
}

export interface MakePurchaseAsSellerResponse {
  success: boolean;
  message: string;
  lockedAmount: number;
  orderAmount: number;
  lockPercentage: number;
}

export const makePurchaseAsBuyer = async (orderId: string) => {
  try {
    const makePurchaseAsBuyerFunction = httpsCallable<
      { buyerId: string; orderId: string },
      MakePurchaseAsBuyerResponse
      // AUGMENT-REFACTOR use cloud function enum from core constants
    >(firebaseFunctions, 'makePurchaseAsBuyer');

    const currentUserId = getCurrentUserId();

    if (!currentUserId) {
      throw new Error('User must be authenticated to make a purchase');
    }

    const result = await makePurchaseAsBuyerFunction({
      buyerId: currentUserId,
      orderId,
    });

    return result.data;
  } catch (error) {
    console.error('Error making purchase as buyer:', error);
    throw error;
  }
};

export const makePurchaseAsSeller = async (orderId: string) => {
  try {
    const makePurchaseAsSellerFunction = httpsCallable<
      { sellerId: string; orderId: string },
      MakePurchaseAsSellerResponse
      // AUGMENT-REFACTOR use cloud function enum from core constants
    >(firebaseFunctions, 'makePurchaseAsSeller');

    const currentUserId = getCurrentUserId();

    if (!currentUserId) {
      throw new Error('User must be authenticated to make a purchase');
    }

    const result = await makePurchaseAsSellerFunction({
      sellerId: currentUserId,
      orderId,
    });

    return result.data;
  } catch (error) {
    console.error('Error making purchase as seller:', error);
    throw error;
  }
};
