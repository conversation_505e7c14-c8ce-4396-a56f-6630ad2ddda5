'use client';

import type { TabType } from '@/app/(app)/marketplace/hooks/use-marketplace-orders';
import { BaseOrderCard } from '@/components/shared/base-order-card';
import { SecondaryMarketBadge } from '@/components/shared/secondary-market-badge';
import { SellButtonComponent } from '@/components/shared/sell-button-component';
import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';
import { isSecondaryMarketOrder } from '@/utils/secondary-market-utils';

interface OrderCardProps {
  order: OrderEntity;
  collection: CollectionEntity | undefined;
  onClick: () => void;
  activeTab?: TabType;
}

// AUGMENT-REFACTOR: this component is redudant, use BaseOrderCard directly, and move <SellButtonComponent order={order} label={buttonLabel} /> inside it
export function OrderCard({
  order,
  collection,
  onClick,
  activeTab,
}: OrderCardProps) {
  const isSecondary = isSecondaryMarketOrder(order);
  const buttonLabel = activeTab === 'sellers' ? 'Fulfill' : 'Buy';

  return (
    <BaseOrderCard
      imageBadge={isSecondary && <SecondaryMarketBadge className="mt-2 ml-2" />}
      order={order}
      collection={collection}
      onClick={onClick}
    >
      <SellButtonComponent order={order} label={buttonLabel} />
    </BaseOrderCard>
  );
}
