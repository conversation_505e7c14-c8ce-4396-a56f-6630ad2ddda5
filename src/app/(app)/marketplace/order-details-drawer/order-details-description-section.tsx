import { Caption } from '@telegram-apps/telegram-ui';

import type { CollectionEntity } from '@/constants/core.constants';

interface OrderDetailsDescriptionSectionProps {
  collection: CollectionEntity | null;
}

{
  /* // AUGMENT-REFACTOR: component is dedudant, move jsx directly in place where it's used */
}
export function OrderDetailsDescriptionSection({
  collection,
}: OrderDetailsDescriptionSectionProps) {
  if (!collection?.description) return null;

  return (
    <div className="text-center">
      <Caption level="2" weight="3" className="text-[#708499]">
        {collection.description}
      </Caption>
    </div>
  );
}
