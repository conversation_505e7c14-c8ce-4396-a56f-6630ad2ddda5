import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';

interface OrderDetailsHeaderSectionProps {
  order: OrderEntity;
  collection: CollectionEntity | null;
}

{
  /* // AUGMENT-REFACTOR: component is dedudant, move jsx directly in place where it's used */
}
export function OrderDetailsHeaderSection({
  collection,
}: OrderDetailsHeaderSectionProps) {
  return (
    <div className="text-center space-y-2">
      <h1 className="text-2xl font-bold text-[#f5f5f5]">
        {collection?.name || 'Unknown Collection'}
      </h1>
    </div>
  );
}
