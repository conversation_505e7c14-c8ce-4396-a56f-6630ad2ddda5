import type { ReactNode } from 'react';

interface OrderDetailsInfoRowProps {
  label: string;
  value: ReactNode;
  isLast?: boolean;
}

{
  /* // AUGMENT-REFACTOR: component is redudant, move jsx directly in place where it's used */
}
export function OrderDetailsInfoRow({
  label,
  value,
  isLast = false,
}: OrderDetailsInfoRowProps) {
  return (
    <div
      className={`flex justify-between items-center py-2 ${
        !isLast ? 'border-b border-[#3a4a5c]/30' : ''
      }`}
    >
      <span className="text-[#f5f5f5] font-medium">{label}</span>
      <div className="text-[#6ab2f2] font-semibold">{value}</div>
    </div>
  );
}
