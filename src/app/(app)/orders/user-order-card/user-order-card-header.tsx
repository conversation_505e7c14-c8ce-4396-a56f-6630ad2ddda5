import { Badge } from '@/components/ui/badge';
import type { OrderEntity } from '@/constants/core.constants';
import { OrderStatus } from '@/constants/core.constants';
import { cn } from '@/lib/utils';
import { formatOrderStatus } from '@/utils/order-utils';

interface UserOrderCardHeaderProps {
  order: OrderEntity;
}

// AUGMENT-REFACTOR: there should be common order-status badge
const getStatusBadgeStyle = (status: OrderStatus) => {
  // AUGMENT-REFACTOR: better to use map instead of swithc-case
  switch (status) {
    case OrderStatus.ACTIVE:
      return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
    case OrderStatus.PAID:
      return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
    case OrderStatus.GIFT_SENT_TO_RELAYER:
      return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
    case OrderStatus.FULFILLED:
      return 'bg-green-500/20 text-green-400 border-green-500/30';
    case OrderStatus.CANCELLED:
      return 'bg-red-500/20 text-red-400 border-red-500/30';
    default:
      return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
  }
};

export function UserOrderCardHeader({ order }: UserOrderCardHeaderProps) {
  return (
    <div className="flex w-full items-center justify-start mb-1 absolute top-0 left-0 px-2 pt-1.5">
      <Badge
        className={cn(
          'text-[10px] px-2 py-0.5 border',
          getStatusBadgeStyle(order.status),
        )}
      >
        {formatOrderStatus(order.status)}
      </Badge>
    </div>
  );
}
