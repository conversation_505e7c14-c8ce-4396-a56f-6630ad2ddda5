'use client';

import { useState } from 'react';
import { Drawer } from 'vaul';

import { SellPriceDetails } from '@/components/shared/sell-price-details';
import { Badge } from '@/components/ui/badge';
import { ResellTxHistory } from '@/components/ui/order/resell-tx-history';
import type { OrderEntity, UserType } from '@/constants/core.constants';
import { OrderStatus } from '@/constants/core.constants';
import { useOrderTimers } from '@/hooks/use-order-timers';
import { useOtherUser } from '@/hooks/use-other-user';
import { useRootContext } from '@/root-context';
import { getStatusConfig } from '@/utils/order-status-utils';
import { shouldShowUserInfo } from '@/utils/order-utils';

import { ResellOrderPriceDrawer } from '../marketplace/resell/resell-order-price-drawer';
import { CancelOrderDrawer } from './cancel-order-drawer';
import {
  UserOrderActionsSection,
  UserOrderDeadlineSection,
  UserOrderImageSection,
  UserOrderPaymentDetailsSection,
  UserOrderSellerEarningsSection,
  UserOrderStatusAlerts,
  UserOrderUserInfoSection,
} from './user-order-details-drawer/';

interface UserOrderDetailsDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  userType: UserType;
  onOrderUpdate: () => void;
}

export function UserOrderDetailsDrawer({
  open,
  onOpenChange,
  order,
  userType,
  onOrderUpdate,
}: UserOrderDetailsDrawerProps) {
  const { collections, currentUser } = useRootContext();
  const [showCancelDrawer, setShowCancelDrawer] = useState(false);
  const [showResellPriceDrawer, setShowResellPriceDrawer] = useState(false);
  const [showResellHistory, setShowResellHistory] = useState(false);

  const collection =
    collections.find((c) => c.id === order?.collectionId) || null;
  const { timeLeft, isFreezed } = useOrderTimers({ order, collection });
  const { otherUser, loadingUser } = useOtherUser({
    order,
    userType,
    isOpen: open,
  });

  const handleCancelOrder = () => setShowCancelDrawer(true);
  const handleCreateSecondaryMarketOrder = () => setShowResellPriceDrawer(true);
  const handleShowResellHistory = () => setShowResellHistory(true);

  const handleOrderCancelled = () => {
    onOrderUpdate();
    onOpenChange(false);
  };

  const handleOrderResold = () => {
    onOrderUpdate();
    setShowResellPriceDrawer(false);
    onOpenChange(false);
  };

  if (!order) return null;

  return (
    <>
      <Drawer.Root open={open} onOpenChange={onOpenChange}>
        <Drawer.Portal>
          <Drawer.Title />
          <Drawer.Overlay className="fixed inset-0 bg-black/40 z-50" />
          <Drawer.Content className="bg-[#17212b] flex flex-col rounded-t-[20px] max-h-[90vh] mt-24 fixed bottom-0 left-0 right-0 z-50">
            <div className="p-6 bg-[#17212b] rounded-t-[20px] flex-1 overflow-y-auto">
              <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />

              <div className="max-w-md mx-auto space-y-6">
                <div className="flex justify-center gap-3 items-center">
                  <h2 className="text-xl font-bold text-[#f5f5f5]">
                    Order #{order.number || order.id?.slice(-6)}
                  </h2>
                  <UserOrderStatusBadge order={order} />
                </div>

                <div className="w-[50%] mx-auto">
                  <UserOrderImageSection collection={collection} />
                </div>

                <SellPriceDetails order={order} className="py-4" />

                <UserOrderPaymentDetailsSection order={order} />

                {(order.status === OrderStatus.PAID ||
                  order.status === OrderStatus.GIFT_SENT_TO_RELAYER) && (
                  <div className="space-y-4">
                    <UserOrderDeadlineSection
                      {...{
                        order,
                        userType,
                        timeLeft,
                      }}
                    />
                    <UserOrderStatusAlerts
                      {...{
                        order,
                        userType,
                        isFreezed,
                      }}
                    />
                  </div>
                )}

                {shouldShowUserInfo(order) && (
                  <UserOrderUserInfoSection
                    {...{
                      otherUser,
                      userType,
                      loadingUser,
                    }}
                  />
                )}

                {/* AUGMENT-REFACTOR: move this condition to constants */}
                {currentUser?.id === order.sellerId &&
                  order.status === OrderStatus.PAID &&
                  order.secondaryMarketPrice &&
                  order.reseller_earnings_for_seller &&
                  order.reseller_earnings_for_seller > 0 && (
                    <UserOrderSellerEarningsSection order={order} />
                  )}

                <UserOrderActionsSection
                  order={order}
                  currentUserId={currentUser?.id}
                  onCancelOrder={handleCancelOrder}
                  onCreateSecondaryMarketOrder={
                    handleCreateSecondaryMarketOrder
                  }
                  onShowResellHistory={handleShowResellHistory}
                />
              </div>
            </div>
          </Drawer.Content>
        </Drawer.Portal>
      </Drawer.Root>

      <CancelOrderDrawer
        open={showCancelDrawer}
        onOpenChange={setShowCancelDrawer}
        order={order}
        onOrderCancelled={handleOrderCancelled}
      />

      <ResellOrderPriceDrawer
        open={showResellPriceDrawer}
        onOpenChange={setShowResellPriceDrawer}
        order={order}
        onOrderResold={handleOrderResold}
      />

      {showResellHistory && order && (
        <div className="fixed inset-0 bg-black/50 z-[60] flex items-center justify-center p-4">
          <div className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <ResellTxHistory
              order={order}
              onClose={() => setShowResellHistory(false)}
            />
          </div>
        </div>
      )}
    </>
  );
}

interface UserOrderStatusBadgeProps {
  order: OrderEntity;
}

// AUGMENT-REFACTOR: I think you need to create common status badge component that will be used in both user orders and marketplace
export function UserOrderStatusBadge({ order }: UserOrderStatusBadgeProps) {
  const statusConfig = getStatusConfig(order.status);

  return (
    <Badge variant="outline" className={statusConfig.className}>
      {statusConfig.label}
    </Badge>
  );
}
