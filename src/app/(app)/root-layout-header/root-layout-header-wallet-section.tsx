import { TonConnectButton } from '@/components/shared/ton-connect-button';

interface WalletSectionProps {
  tonWalletAddress: string;
  isConnecting: boolean;
  isAuthenticating: boolean;
  showWalletDropdown: boolean;
  setShowWalletDropdown: (show: boolean) => void;
  dropdownRef: React.RefObject<HTMLDivElement | null>;
  onWalletAction: () => Promise<string | void>;
  onDisconnectWallet: () => Promise<void>;
  formatAddress: (address: string) => string;
}

// AUGMENT-REFACTOR redudant component, user ton connect button directly, get rid of this
export function WalletSection({
  tonWalletAddress,
  isConnecting,
  isAuthenticating,
  showWalletDropdown,
  setShowWalletDropdown,
  dropdownRef,
  onWalletAction,
  onDisconnectWallet,
  formatAddress,
}: WalletSectionProps) {
  return (
    <TonConnectButton
      tonWalletAddress={tonWalletAddress}
      isConnecting={isConnecting}
      isAuthenticating={isAuthenticating}
      showWalletDropdown={showWalletDropdown}
      setShowWalletDropdown={setShowWalletDropdown}
      dropdownRef={dropdownRef}
      onWalletAction={onWalletAction}
      onDisconnectWallet={onDisconnectWallet}
      formatAddress={formatAddress}
    />
  );
}
