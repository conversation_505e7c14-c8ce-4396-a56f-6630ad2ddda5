import { Badge } from '@/components/ui/badge';
import type { OrderEntity, OrderStatus } from '@/constants/core.constants';
import { cn } from '@/lib/utils';
import { getStatusConfig } from '@/utils/order-status-utils';

interface OrderStatusBadgeProps {
  order: OrderEntity;
  variant?: 'default' | 'pill';
  className?: string;
}

interface StatusBadgeProps {
  status: OrderStatus;
  variant?: 'default' | 'pill';
  className?: string;
}

export function OrderStatusBadge({ 
  order, 
  variant = 'default',
  className 
}: OrderStatusBadgeProps) {
  return (
    <StatusBadge 
      status={order.status} 
      variant={variant}
      className={className}
    />
  );
}

export function StatusBadge({ 
  status, 
  variant = 'default',
  className 
}: StatusBadgeProps) {
  const statusConfig = getStatusConfig(status);

  const baseClasses = variant === 'pill' 
    ? 'text-[10px] px-2 py-0.5 border'
    : '';

  return (
    <Badge 
      variant="outline" 
      className={cn(
        baseClasses,
        statusConfig.className,
        className
      )}
    >
      {statusConfig.label}
    </Badge>
  );
}
