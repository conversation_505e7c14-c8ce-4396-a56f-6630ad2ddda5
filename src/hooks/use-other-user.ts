import { useEffect, useState } from 'react';

import { getUserById } from '@/api/auth-api';
import type {
  OrderEntity,
  UserEntity,
  UserType,
} from '@/constants/core.constants';
import { getOtherUserId } from '@/utils/order-utils';

interface UseOtherUserProps {
  order: OrderEntity | null;
  userType: UserType;
  isOpen: boolean;
}

// AUGMENT-REFACTOR: rename it - I don't understand what it does
export function useOtherUser({ order, userType, isOpen }: UseOtherUserProps) {
  const [otherUser, setOtherUser] = useState<UserEntity | null>(null);
  const [loadingUser, setLoadingUser] = useState(false);

  useEffect(() => {
    const fetchOtherUser = async () => {
      if (!order) return;

      const otherUserId = getOtherUserId(order, userType);
      if (!otherUserId) return;

      setLoadingUser(true);
      try {
        const user = await getUserById(otherUserId);
        setOtherUser(user);
      } catch (error) {
        console.error('Error fetching user:', error);
        setOtherUser(null);
      } finally {
        setLoadingUser(false);
      }
    };

    if (isOpen) {
      fetchOtherUser();
    } else {
      setOtherUser(null);
      setLoadingUser(false);
    }
  }, [order, userType, isOpen]);

  return { otherUser, loadingUser };
}
