import type { OrderEntity, UserEntity, UserType } from '@/constants/core.constants';
import { OrderStatus } from '@/constants/core.constants';

export interface CollateralCalculation {
  sellerLockedAmount: number;
  buyerLockedAmount: number;
  purchaseFeeAmount: number;
  resellPurchaseFeeAmount: number;
}

export function formatBPSToPercent(bps: number): string {
  return (bps / 100).toFixed(1);
}

export function calculateCollateralLoss(
  order: OrderEntity | null,
  currentUser: UserEntity | null
): number {
  if (!order || !currentUser) return 0;

  if (order.status === OrderStatus.PAID) {
    if (currentUser.id === order.buyerId) {
      return order.price; // Buyer loses full payment
    } else if (currentUser.id === order.sellerId) {
      const sellerLockPercentage =
        (order.fees?.seller_locked_percentage ?? 0) / 10000;
      return order.price * sellerLockPercentage; // Seller loses locked collateral
    }
  }
  return 0;
}

export function isOriginalSeller(
  currentUser: UserEntity | null,
  order: OrderEntity | null
): boolean {
  return currentUser?.id === order?.sellerId;
}

export function hasResellerEarnings(order: OrderEntity | null): boolean {
  return !!(
    order?.reseller_earnings_for_seller &&
    order.reseller_earnings_for_seller > 0
  );
}

export function getUserLabel(userType: UserType): string {
  return userType === 'seller' ? 'Buyer' : 'Seller';
}

export function calculateOrderAmounts(
  order: OrderEntity,
  showPurchaseFee: boolean = false,
  hasSecondaryPrice: boolean = false
): CollateralCalculation {
  const fees = order.fees;
  if (!fees) {
    return {
      sellerLockedAmount: 0,
      buyerLockedAmount: 0,
      purchaseFeeAmount: 0,
      resellPurchaseFeeAmount: 0,
    };
  }

  const sellerLockedAmount = (order.price * fees.seller_locked_percentage) / 10000;
  const buyerLockedAmount = (order.price * fees.buyer_locked_percentage) / 10000;
  const purchaseFeeAmount = showPurchaseFee
    ? (order.price * fees.purchase_fee) / 10000
    : 0;
  const resellPurchaseFeeAmount = hasSecondaryPrice
    ? ((order.secondaryMarketPrice || 0) * fees.resell_purchase_fee) / 10000
    : 0;

  return {
    sellerLockedAmount,
    buyerLockedAmount,
    purchaseFeeAmount,
    resellPurchaseFeeAmount,
  };
}

export function shouldShowSellerEarnings(
  currentUser: UserEntity | null,
  order: OrderEntity
): boolean {
  return !!(
    currentUser?.id === order.sellerId &&
    order.status === OrderStatus.PAID &&
    order.secondaryMarketPrice &&
    order.reseller_earnings_for_seller &&
    order.reseller_earnings_for_seller > 0
  );
}
